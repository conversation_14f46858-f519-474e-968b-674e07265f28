import { useState, useCallback, useEffect } from 'react';
import { bookmarkEntity, unbookmarkEntity, getBookmarkedEntities } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';
import { Entity } from '@/types/entity';

interface UseBookmarksReturn {
  bookmarkedEntityIds: Set<string>;
  isBookmarked: (entityId: string) => boolean;
  toggleBookmark: (entityId: string) => Promise<void>;
  isLoading: boolean;
  error: string | null;
  refreshBookmarks: () => Promise<void>;
}

export const useBookmarks = (): UseBookmarksReturn => {
  const { session, isLoading: isAuthLoading } = useAuth();
  const [bookmarkedEntityIds, setBookmarkedEntityIds] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refreshBookmarks = useCallback(async () => {
    if (!session?.access_token) {
      setBookmarkedEntityIds(new Set());
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Fetch bookmarked entities with proper pagination (max 100 per request)
      const allEntityIds = new Set<string>();
      let currentPage = 1;
      let hasMorePages = true;
      const maxLimit = 100; // Backend maximum limit

      while (hasMorePages) {
        const bookmarksResponse = await getBookmarkedEntities(
          session.access_token,
          currentPage,
          maxLimit
        );

        // CRITICAL FIX: Check for valid response structure
        if (bookmarksResponse && bookmarksResponse.data && Array.isArray(bookmarksResponse.data)) {
          // Add entity IDs from this page
          bookmarksResponse.data.forEach((entity: Entity) => {
            allEntityIds.add(entity.id);
          });

          // Check if there are more pages - handle different meta formats with safe fallbacks
          const meta = bookmarksResponse.meta || {};
          const totalPages = meta.totalPages || meta.total_pages || 1;
          const responsePage = meta.page || currentPage;
          hasMorePages = responsePage < totalPages;
          currentPage++;
        } else {
          // Handle unexpected API response structure
          console.error('Invalid response structure from getBookmarkedEntities', bookmarksResponse);
          hasMorePages = false; // Stop pagination loop
        }

        // Safety check to prevent infinite loops
        if (currentPage > 50) { // Max 5000 bookmarks (50 * 100)
          console.warn('Reached maximum page limit for bookmark fetching');
          break;
        }
      }

      setBookmarkedEntityIds(allEntityIds);
    } catch (err) {
      console.error('Failed to fetch bookmarks:', err);

      // Don't set error for authentication issues - just clear bookmarks
      if (err instanceof Error && err.message.includes('Unauthorized')) {
        console.log('[useBookmarks] Authentication error, clearing bookmarks');
        setBookmarkedEntityIds(new Set());
      } else {
        setError(err instanceof Error ? err.message : 'Failed to fetch bookmarks');
      }
    } finally {
      setIsLoading(false);
    }
  }, [session?.access_token]);

  const isBookmarked = useCallback((entityId: string): boolean => {
    return bookmarkedEntityIds.has(entityId);
  }, [bookmarkedEntityIds]);

  const toggleBookmark = useCallback(async (entityId: string): Promise<void> => {
    if (!session?.access_token) {
      throw new Error('You must be signed in to bookmark entities');
    }

    setIsLoading(true);
    setError(null);

    try {
      const wasBookmarked = bookmarkedEntityIds.has(entityId);
      
      if (wasBookmarked) {
        await unbookmarkEntity(entityId, session.access_token);
        setBookmarkedEntityIds(prev => {
          const newSet = new Set(prev);
          newSet.delete(entityId);
          return newSet;
        });
      } else {
        await bookmarkEntity(entityId, session.access_token);
        setBookmarkedEntityIds(prev => new Set(prev).add(entityId));
      }
    } catch (err) {
      console.error('Failed to toggle bookmark:', err);
      setError(err instanceof Error ? err.message : 'Failed to update bookmark');
      throw err; // Re-throw so components can handle the error
    } finally {
      setIsLoading(false);
    }
  }, [session?.access_token, bookmarkedEntityIds]);

  // Load bookmarks when user signs in - but wait for auth to be confirmed
  useEffect(() => {
    // DO NOT FETCH if auth state is still being determined
    if (isAuthLoading) {
      return;
    }

    if (session) {
      refreshBookmarks();
    } else {
      // Clear data if user logs out
      setBookmarkedEntityIds(new Set());
    }
  }, [session, isAuthLoading, refreshBookmarks]); // Add isAuthLoading to dependency array

  return {
    bookmarkedEntityIds,
    isBookmarked,
    toggleBookmark,
    isLoading,
    error,
    refreshBookmarks,
  };
};
